import { NextResponse } from 'next/server';
import { crawlAndIndexWebsite, getChatbotStats } from '@/lib/chatbotService';

/**
 * Trigger website crawling and indexing
 * @route POST /api/chatbot/crawl
 */
export async function POST(request) {
  try {
    const { startUrl, options = {} } = await request.json();

    // Validate start URL
    if (!startUrl) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Start URL is required' 
        },
        { status: 400 }
      );
    }

    // Validate URL format
    try {
      new URL(startUrl);
    } catch (error) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid URL format' 
        },
        { status: 400 }
      );
    }

    // Default crawl options
    const defaultOptions = {
      maxPages: 50,
      delay: 1000,
      maxDepth: 3
    };

    const crawlOptions = { ...defaultOptions, ...options };

    // Validate options
    if (crawlOptions.maxPages > 100) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Maximum pages limit is 100' 
        },
        { status: 400 }
      );
    }

    console.log(`Starting crawl for: ${startUrl}`);
    console.log('Crawl options:', crawlOptions);

    // Start the crawling process
    const result = await crawlAndIndexWebsite(startUrl, crawlOptions);

    if (!result.success) {
      return NextResponse.json(
        {
          success: false,
          error: result.error || 'Crawling failed'
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Website crawling and indexing completed',
      results: {
        crawled: result.crawled,
        indexed: result.indexed,
        errors: result.errors,
        startUrl: result.startUrl,
        completedAt: result.completedAt
      },
      details: result.results
    });

  } catch (error) {
    console.error('Crawl API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'An unexpected error occurred during crawling',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

/**
 * Get crawling status and statistics
 * @route GET /api/chatbot/crawl
 */
export async function GET() {
  try {
    const stats = await getChatbotStats();
    
    return NextResponse.json({
      success: true,
      stats: stats.stats || {
        totalPages: 0,
        totalChunks: 0,
        lastCrawl: null,
        status: 'not_crawled'
      },
      recommendations: {
        suggestedStartUrl: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
        recommendedOptions: {
          maxPages: 30,
          delay: 1000,
          maxDepth: 2
        }
      }
    });

  } catch (error) {
    console.error('Error getting crawl status:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get crawl status'
      },
      { status: 500 }
    );
  }
}
