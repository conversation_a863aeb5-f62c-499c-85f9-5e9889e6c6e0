import { NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import { collection, getDocs, query, orderBy, limit } from 'firebase/firestore';

/**
 * Get chatbot system status and statistics
 * @route GET /api/chatbot/status
 */
export async function GET() {
  try {
    // Get page statistics
    const pagesSnapshot = await getDocs(collection(db, 'chatbot_pages'));
    const totalPages = pagesSnapshot.size;

    // Get chunk statistics
    const chunksSnapshot = await getDocs(collection(db, 'chatbot_chunks'));
    const totalChunks = chunksSnapshot.size;

    // Get recent crawl information
    let lastCrawl = null;
    let recentPages = [];
    
    if (totalPages > 0) {
      const recentPagesQuery = query(
        collection(db, 'chatbot_pages'),
        orderBy('lastIndexed', 'desc'),
        limit(5)
      );
      
      const recentPagesSnapshot = await getDocs(recentPagesQuery);
      recentPages = recentPagesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      if (recentPages.length > 0) {
        lastCrawl = recentPages[0].lastIndexed;
      }
    }

    // Calculate system health
    const systemHealth = {
      status: totalPages > 0 ? 'healthy' : 'needs_indexing',
      coverage: totalPages > 10 ? 'good' : totalPages > 5 ? 'moderate' : 'low',
      readiness: totalChunks > 50 ? 'ready' : 'limited'
    };

    return NextResponse.json({
      success: true,
      stats: {
        totalPages,
        totalChunks,
        lastCrawl,
        averageChunksPerPage: totalPages > 0 ? Math.round(totalChunks / totalPages) : 0
      },
      health: systemHealth,
      recentPages: recentPages.map(page => ({
        url: page.url,
        title: page.title,
        lastIndexed: page.lastIndexed,
        chunkCount: page.chunkCount
      })),
      recommendations: getRecommendations(totalPages, totalChunks, lastCrawl),
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error getting chatbot status:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get system status',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

/**
 * Generate recommendations based on current system state
 */
function getRecommendations(totalPages, totalChunks, lastCrawl) {
  const recommendations = [];
  
  if (totalPages === 0) {
    recommendations.push({
      type: 'critical',
      message: 'No pages indexed. Run initial crawl to enable chatbot functionality.',
      action: 'Start crawling from your website homepage'
    });
  } else if (totalPages < 10) {
    recommendations.push({
      type: 'warning',
      message: 'Limited page coverage. Consider crawling more pages for better responses.',
      action: 'Increase maxPages in crawl configuration'
    });
  }
  
  if (totalChunks < 50) {
    recommendations.push({
      type: 'info',
      message: 'Low content volume. More content will improve response quality.',
      action: 'Crawl additional pages or increase content depth'
    });
  }
  
  if (lastCrawl) {
    const daysSinceLastCrawl = Math.floor(
      (new Date() - new Date(lastCrawl)) / (1000 * 60 * 60 * 24)
    );
    
    if (daysSinceLastCrawl > 7) {
      recommendations.push({
        type: 'warning',
        message: `Content is ${daysSinceLastCrawl} days old. Consider re-crawling for fresh content.`,
        action: 'Run a fresh crawl to update indexed content'
      });
    }
  }
  
  if (recommendations.length === 0) {
    recommendations.push({
      type: 'success',
      message: 'System is healthy and ready to serve users.',
      action: 'Monitor usage and update content as needed'
    });
  }
  
  return recommendations;
}
