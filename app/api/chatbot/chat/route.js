import { NextResponse } from 'next/server';
import { processChatQuery } from '@/lib/chatbotService';

/**
 * Handle chatbot conversations
 * @route POST /api/chatbot/chat
 */
export async function POST(request) {
  try {
    const { message, conversationId } = await request.json();

    // Validate input
    if (!message || typeof message !== 'string' || message.trim().length === 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Message is required and must be a non-empty string' 
        },
        { status: 400 }
      );
    }

    // Limit message length
    if (message.length > 1000) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Message is too long. Please keep it under 1000 characters.' 
        },
        { status: 400 }
      );
    }

    console.log(`Chatbot query: "${message.substring(0, 100)}..."`);

    // Process the chat query
    const result = await processChatQuery(message.trim());

    if (!result.success) {
      return NextResponse.json(
        {
          success: false,
          error: result.error || 'Failed to process your question'
        },
        { status: 500 }
      );
    }

    // Return successful response
    return NextResponse.json({
      success: true,
      response: result.response,
      sources: result.sources || [],
      metadata: {
        relevantChunks: result.relevantChunks || 0,
        conversationId: conversationId || null,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Chatbot API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'An unexpected error occurred. Please try again.',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

/**
 * Get chatbot status and capabilities
 * @route GET /api/chatbot/chat
 */
export async function GET() {
  try {
    return NextResponse.json({
      success: true,
      status: 'online',
      capabilities: [
        'Answer questions about MoneyTales website',
        'Provide information about features and services',
        'Help with navigation and usage',
        'Explain PDF processing and AI scenario generation'
      ],
      limits: {
        maxMessageLength: 1000,
        responseTimeout: 30000
      },
      lastUpdated: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting chatbot status:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get chatbot status'
      },
      { status: 500 }
    );
  }
}
