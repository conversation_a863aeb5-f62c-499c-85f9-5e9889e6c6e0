'use client';
import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import {
  generateEmbeddingsForChunks,
  rankChunksByRelevance
} from './utils/embeddingAnalysis';
import AnalysisResults from './components/AnalysisResults';
import EmbeddingsDisplay from './components/EmbeddingsDisplay';

export default function Home() {
  const [isClient, setIsClient] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [prompt, setPrompt] = useState('');
  const [file, setFile] = useState(null);
  const [fileName, setFileName] = useState('');
  const [error, setError] = useState('');
  const [outlineData, setOutlineData] = useState(null);
  const [totalPages, setTotalPages] = useState(null);
  const [pdfChunks, setPdfChunks] = useState(null);
  const [chunksWithEmbeddings, setChunksWithEmbeddings] = useState(null);
  const [isGeneratingEmbeddings, setIsGeneratingEmbeddings] = useState(false);
  const [embeddingsGenerated, setEmbeddingsGenerated] = useState(false);
  const [embeddingProgress, setEmbeddingProgress] = useState({ current: 0, total: 0, chunkId: '' });
  const [analysisResults, setAnalysisResults] = useState(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const fileInputRef = useRef(null);
  const [pdfInstance, setPdfInstance] = useState(null);

  // Function to extract text from a page
  const extractTextFromPage = async (pageNum) => {
    if (!pdfInstance) return '';

    try {
      const page = await pdfInstance.getPage(pageNum);
      const textContent = await page.getTextContent();
      return textContent.items.map(item => item.str).join(' ');
    } catch (error) {
      console.error(`Error extracting text from page ${pageNum}:`, error);
      return '';
    }
  };

  // Function to extract text from a range of pages
  const extractTextFromPageRange = async (startPage, endPage) => {
    if (!pdfInstance) return '';

    const textPromises = [];
    for (let i = startPage; i <= endPage; i++) {
      textPromises.push(extractTextFromPage(i));
    }
    const pageTexts = await Promise.all(textPromises);
    return pageTexts.join('\n\n');
  };

  useEffect(() => {
    setIsClient(true);
  }, []);

  const handlePromptChange = (e) => {
    setPrompt(e.target.value);
  };

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    if (selectedFile) {
      if (selectedFile.type !== 'application/pdf') {
        setError('Please upload a PDF file');
        setFile(null);
        setFileName('');
        return;
      }

      if (selectedFile.size > 30 * 1024 * 1024) { // 30MB limit
        setError('File size should be less than 30MB');
        setFile(null);
        setFileName('');
        return;
      }

      setFile(selectedFile);
      setFileName(selectedFile.name);
      setError('');
      setOutlineData(null);
      setTotalPages(null);
      setPdfChunks(null);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const droppedFile = e.dataTransfer.files[0];

      if (droppedFile.type !== 'application/pdf') {
        setError('Please upload a PDF file');
        return;
      }

      if (droppedFile.size > 30 * 1024 * 1024) { // 30MB limit
        setError('File size should be less than 30MB');
        return;
      }

      setFile(droppedFile);
      setFileName(droppedFile.name);
      setError('');
      setOutlineData(null);
      setTotalPages(null);
      setPdfChunks(null);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!file) {
      setError('Please upload a PDF file first');
      return;
    }

    setIsLoading(true);
    setError('');
    setAnalysisResults(null);
    setChunksWithEmbeddings(null);
    setEmbeddingsGenerated(false);

    try {
      // Import PDF.js
      const pdfJS = await import('pdfjs-dist/build/pdf');
      pdfJS.GlobalWorkerOptions.workerSrc = window.location.origin + '/pdf.worker.min.mjs';

      // Convert File object to ArrayBuffer
      const arrayBuffer = await file.arrayBuffer();

      // Load the PDF document using the array buffer
      const pdf = await pdfJS.getDocument({ data: arrayBuffer }).promise;
      setPdfInstance(pdf);

      // Get total pages
      const numPages = pdf.numPages;
      setTotalPages(numPages);

      // Extract outline
      const outline = await pdf.getOutline();
      console.log('Outline:', outline);
      console.log('Form submitted with:', { prompt, file });

      // Process outline to extract page numbers
      const processOutlineItems = async (items) => {
        const result = [];

        for (const item of items) {
          const processedItem = { ...item };

          // Extract page number from destination if available
          if (item.dest) {
            try {
              // If dest is a string, we need to resolve it first
              let destRef = item.dest;
              if (typeof destRef === 'string') {
                destRef = await pdf.getDestination(destRef);
              }

              // The first element in the destination array is the page reference
              if (Array.isArray(destRef) && destRef.length > 0) {
                const pageRef = destRef[0];
                // Get the page number (add 1 because PDF.js uses 0-based indices)
                const pageNum = await pdf.getPageIndex(pageRef) + 1;
                processedItem.pageNumber = pageNum;
              }
            } catch (error) {
              console.error('Error extracting page number:', error);
            }
          }

          // Process nested items recursively
          if (item.items && item.items.length > 0) {
            processedItem.items = await processOutlineItems(item.items);
          }

          result.push(processedItem);
        }

        return result;
      };

      // Set the outline data for display
      if (!outline || outline.length === 0) {
        console.log('No outline found in the PDF');
        setOutlineData({ items: [], message: 'No outline found in this PDF document. Creating default chunks of 20 pages each.' });

        try {
          // Create default chunks of 20 pages each when no TOC is found
          const chunkSize = 20; // Changed from 10 to 20 pages
          const defaultChunks = [];

          for (let i = 1; i <= numPages; i += chunkSize) {
            const startPage = i;
            const endPage = Math.min(i + chunkSize - 1, numPages);

            // Extract text content from the page range
            const content = await extractTextFromPageRange(startPage, endPage);

            defaultChunks.push({
              id: `default-chunk-${Math.floor(i / chunkSize) + 1}`,
              title: `Pages ${startPage}-${endPage}`,
              path: [`Pages ${startPage}-${endPage}`],
              startPage: startPage,
              endPage: endPage,
              pageCount: endPage - startPage + 1,
              isDefaultChunk: true,
              content: content
            });
          }

          setPdfChunks(defaultChunks);
          console.log('Created default chunks successfully:', defaultChunks.length);
        } catch (chunkError) {
          console.error('Error creating default chunks:', chunkError);
          setError(`Error creating default chunks: ${chunkError.message}`);
        }
      } else {
        const processedOutline = await processOutlineItems(outline);
        setOutlineData({ items: processedOutline, message: '' });

        // Using the extractTextFromPageRange function defined at the component level

        // Create PDF chunks based on the deepest sections in the outline
        const createPdfChunks = async (outlineItems) => {
          // Find all sections at the deepest level with page numbers
          const deepestSections = [];

          const findDeepestSections = (items, depth = 0, path = []) => {
            for (let i = 0; i < items.length; i++) {
              const item = items[i];
              const currentPath = [...path, item.title];

              if (item.items && item.items.length > 0) {
                // This item has children, go deeper
                findDeepestSections(item.items, depth + 1, currentPath);
              } else if (item.pageNumber) {
                // This is a leaf node with a page number
                deepestSections.push({
                  title: item.title,
                  pageNumber: item.pageNumber,
                  depth: depth,
                  path: currentPath
                });
              }
            }
          };

          findDeepestSections(outlineItems);

          // Sort sections by page number
          deepestSections.sort((a, b) => a.pageNumber - b.pageNumber);

          // Create chunks based on page ranges
          const chunks = [];
          for (let i = 0; i < deepestSections.length; i++) {
            const section = deepestSections[i];
            const nextSection = deepestSections[i + 1];

            const startPage = section.pageNumber;
            // If this is the last section, the end page is the total number of pages
            // Otherwise, it's the page before the next section starts
            const endPage = nextSection ? nextSection.pageNumber - 1 : numPages;

            // Extract text content from the page range
            const content = await extractTextFromPageRange(startPage, endPage);

            chunks.push({
              id: `chunk-${i + 1}`,
              title: section.title,
              path: section.path,
              startPage: startPage,
              endPage: endPage,
              pageCount: endPage - startPage + 1,
              content: content
            });
          }

          return chunks;
        };

        // Create chunks and handle async operation
        const chunks = await createPdfChunks(processedOutline);

        // If no chunks were created (no deep sections with page numbers),
        // create default chunks based on total pages
        if (chunks.length === 0) {
          // Create chunks of approximately 20 pages each
          const chunkSize = 20; // Changed from 10 to 20 pages
          const defaultChunks = [];

          for (let i = 1; i <= numPages; i += chunkSize) {
            const startPage = i;
            const endPage = Math.min(i + chunkSize - 1, numPages);

            // Extract text content from the page range
            const content = await extractTextFromPageRange(startPage, endPage);

            defaultChunks.push({
              id: `default-chunk-${Math.floor(i / chunkSize) + 1}`,
              title: `Pages ${startPage}-${endPage}`,
              path: [`Pages ${startPage}-${endPage}`],
              startPage: startPage,
              endPage: endPage,
              pageCount: endPage - startPage + 1,
              isDefaultChunk: true,
              content: content
            });
          }

          setPdfChunks(defaultChunks);
        } else {
          setPdfChunks(chunks);
        }
      }

      // Reset embeddings and analysis states
      setChunksWithEmbeddings(null);
      setEmbeddingsGenerated(false);
      setAnalysisResults(null);

    } catch (error) {
      console.error('Error submitting form:', error);
      setError(`Error processing PDF: ${error.message}`);
      setPdfChunks(null);
      setChunksWithEmbeddings(null);
      setAnalysisResults(null);
    } finally {
      setIsLoading(false);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current.click();
  };

  // Function to manually generate embeddings
  const handleGenerateEmbeddings = async () => {
    if (!pdfChunks || pdfChunks.length === 0) {
      setError('No PDF chunks available. Please process a PDF first.');
      return;
    }

    setIsGeneratingEmbeddings(true);
    setError('');
    setEmbeddingProgress({ current: 0, total: pdfChunks.length, chunkId: '' });

    try {
      console.log('Manually generating embeddings for chunks...');

      // Progress callback function
      const onProgress = (current, total, chunkId) => {
        setEmbeddingProgress({ current, total, chunkId });
      };

      const embedded = await generateEmbeddingsForChunks(pdfChunks, onProgress);
      setChunksWithEmbeddings(embedded);
      setEmbeddingsGenerated(true);
      console.log('Embeddings generated successfully');

      // Don't auto-analyze - let user click the analyze button manually
    } catch (error) {
      console.error('Error generating embeddings:', error);
      setError(`Error generating embeddings: ${error.message}`);
    } finally {
      setIsGeneratingEmbeddings(false);
      setEmbeddingProgress({ current: 0, total: 0, chunkId: '' });
    }
  };

  // Function to analyze the prompt against embeddings
  const handleAnalyzePrompt = async (embeddedChunks) => {
    if (!prompt || !prompt.trim()) {
      setError('Please enter a prompt to analyze.');
      return;
    }

    if (!embeddedChunks || embeddedChunks.length === 0) {
      setError('No embeddings available. Please generate embeddings first.');
      return;
    }

    setIsAnalyzing(true);
    setError('');

    try {
      console.log('Analyzing prompt against embeddings...');
      const results = await rankChunksByRelevance(prompt, embeddedChunks);
      setAnalysisResults(results);

      // Save analysis results and prompt to localStorage for use in the alternate scenario page
      try {
        localStorage.setItem('analysisResults', JSON.stringify(results));
        localStorage.setItem('userPrompt', prompt);
        console.log('Analysis results and prompt saved to localStorage');
      } catch (storageError) {
        console.error('Error saving data to localStorage:', storageError);
      }

      console.log('Analysis complete');
    } catch (error) {
      console.error('Error analyzing prompt:', error);
      setError(`Error analyzing prompt: ${error.message}`);
    } finally {
      setIsAnalyzing(false);
    }
  };

  if (!isClient) {
    return null;
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="bg-primary-green text-white p-4">
        <div className="container mx-auto flex justify-between items-center">
          <h1 className="font-slackey text-2xl">MoneyTales</h1>
          <nav className="flex space-x-4">
            <Link href="/alternate-scenario" className="hover:text-gray-200 transition-colors">
              AI Scenarios
            </Link>
            <Link href="/game" className="hover:text-gray-200 transition-colors">
              Game Demo
            </Link>
          </nav>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto py-8 px-4">
        <div className="max-w-2xl mx-auto">
          <h1 className="font-libre text-3xl font-bold mb-6 text-text-primary">PDF Outline & Chunking Tool</h1>

          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <form onSubmit={handleSubmit}>
              {/* Prompt Input */}
              <div className="mb-6">
                <label htmlFor="prompt" className="block text-gray-700 font-medium mb-2">
                  Enter your prompt
                </label>
                <textarea
                  id="prompt"
                  value={prompt}
                  onChange={handlePromptChange}
                  placeholder="What would you like to know about this PDF?"
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent min-h-[120px]"
                />
              </div>

              {/* File Upload */}
              <div className="mb-6">
                <label className="block text-gray-700 font-medium mb-2">
                  Upload PDF
                </label>
                <div
                  className={`border-2 border-dashed rounded-md p-8 text-center cursor-pointer transition-colors ${
                    error && !file ? 'border-error' : 'border-gray-300 hover:border-primary-green'
                  }`}
                  onDragOver={handleDragOver}
                  onDrop={handleDrop}
                  onClick={triggerFileInput}
                >
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileChange}
                    accept="application/pdf"
                    className="hidden"
                  />

                  {!file ? (
                    <div>
                      <div className="flex justify-center mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                        </svg>
                      </div>
                      <p className="text-gray-600 mb-1">Drag and drop your PDF here, or click to browse</p>
                      <p className="text-gray-500 text-sm">Maximum file size: 30MB</p>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center space-x-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-primary-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      <div className="text-left">
                        <p className="text-primary-green font-medium">{fileName}</p>
                        <button
                          type="button"
                          className="text-sm text-gray-500 hover:text-error"
                          onClick={(e) => {
                            e.stopPropagation();
                            setFile(null);
                            setFileName('');
                            setOutlineData(null);
                            setTotalPages(null);
                            setPdfChunks(null);
                          }}
                        >
                          Remove
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Error Message */}
              {error && (
                <div className="mb-4 p-3 bg-error/10 text-error rounded-md">
                  {error}
                </div>
              )}

              {/* Submit Button */}
              <button
                type="submit"
                disabled={isLoading}
                className={`w-full px-4 py-3 rounded-md font-medium transition-all duration-300 ${
                  isLoading
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-primary-green text-white hover:bg-primary-green-dark'
                }`}
              >
                {isLoading ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {isGeneratingEmbeddings
                      ? 'Generating Embeddings...'
                      : isAnalyzing
                        ? 'Analyzing Prompt...'
                        : 'Processing PDF...'}
                  </span>
                ) : (
                  'Process PDF & Analyze Prompt'
                )}
              </button>

              {/* Processing Status */}
              {isLoading && (
                <div className="mt-4 text-sm text-gray-600">
                  <div className="flex items-center">
                    <div className="w-full bg-gray-200 rounded-full h-2.5 mr-2">
                      <div className="bg-primary-green h-2.5 rounded-full animate-pulse" style={{
                        width: isGeneratingEmbeddings
                          ? '75%'
                          : isAnalyzing
                            ? '90%'
                            : '50%'
                      }}></div>
                    </div>
                    <span className="whitespace-nowrap">
                      {isGeneratingEmbeddings
                        ? 'Generating embeddings...'
                        : isAnalyzing
                          ? 'Analyzing prompt...'
                          : 'Extracting PDF content...'}
                    </span>
                  </div>
                </div>
              )}
            </form>
          </div>

          {/* Outline Display Section */}
          {outlineData && (
            <div className="bg-white rounded-lg shadow-md p-6 mb-8">
              <div className="flex justify-between items-center mb-4">
                <h2 className="font-libre text-2xl font-bold text-text-primary">PDF Outline</h2>
                {totalPages && (
                  <span className="text-sm bg-gray-100 px-3 py-1 rounded-full text-gray-600">
                    Total pages: {totalPages}
                  </span>
                )}
              </div>

              {outlineData.message ? (
                <p className="text-gray-600">{outlineData.message}</p>
              ) : (
                <div className="space-y-2">
                  {outlineData.items.length > 0 ? (
                    <div className="max-h-[400px] overflow-y-auto">
                      <ul className="space-y-2">
                        {outlineData.items.map((item, index) => (
                          <li key={index} className="border-l-2 border-primary-green pl-3 py-1">
                            <div className="flex justify-between items-center">
                              <p className="font-medium">{item.title}</p>
                              {item.pageNumber && (
                                <span className="text-sm text-gray-500 ml-2 bg-gray-100 px-2 py-0.5 rounded-md">
                                  Page {item.pageNumber}
                                </span>
                              )}
                            </div>
                            {item.items && item.items.length > 0 && (
                              <ul className="pl-4 mt-1 space-y-1">
                                {item.items.map((subItem, subIndex) => (
                                  <li key={`${index}-${subIndex}`} className="border-l border-primary-green/50 pl-2 py-0.5">
                                    <div className="flex justify-between items-center">
                                      <p>{subItem.title}</p>
                                      {subItem.pageNumber && (
                                        <span className="text-xs text-gray-500 ml-2 bg-gray-100 px-1.5 py-0.5 rounded-md">
                                          Page {subItem.pageNumber}
                                        </span>
                                      )}
                                    </div>
                                    {/* Handle deeper nesting levels */}
                                    {subItem.items && subItem.items.length > 0 && (
                                      <ul className="pl-3 mt-1 space-y-1">
                                        {subItem.items.map((nestedItem, nestedIndex) => (
                                          <li key={`${index}-${subIndex}-${nestedIndex}`} className="border-l border-primary-green/30 pl-2 py-0.5">
                                            <div className="flex justify-between items-center">
                                              <p className="text-sm">{nestedItem.title}</p>
                                              {nestedItem.pageNumber && (
                                                <span className="text-xs text-gray-500 ml-2 bg-gray-100 px-1.5 py-0.5 rounded-md">
                                                  Page {nestedItem.pageNumber}
                                                </span>
                                              )}
                                            </div>
                                          </li>
                                        ))}
                                      </ul>
                                    )}
                                  </li>
                                ))}
                              </ul>
                            )}
                          </li>
                        ))}
                      </ul>
                    </div>
                  ) : (
                    <p className="text-gray-600">No outline structure found in this PDF.</p>
                  )}
                </div>
              )}
            </div>
          )}

          {/* PDF Chunks Display Section */}
          {pdfChunks && pdfChunks.length > 0 && (
            <>
              <div className="bg-white rounded-lg shadow-md p-6 mb-8">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="font-libre text-2xl font-bold text-text-primary">PDF Chunks</h2>
                  <span className="text-sm bg-gray-100 px-3 py-1 rounded-full text-gray-600">
                    {pdfChunks.length} chunks created
                  </span>
                </div>

                <div className="mb-4">
                  <p className="text-gray-600 mb-2">
                    {pdfChunks[0]?.isDefaultChunk
                      ? `The PDF has been divided into ${pdfChunks.length} default chunks of approximately 20 pages each. This happens when no table of contents (TOC) is found in the PDF.`
                      : `The PDF has been divided into ${pdfChunks.length} chunks based on the deepest sections in the outline.`
                    }
                  </p>
                  <div className="flex flex-wrap gap-2 mb-4">
                    {pdfChunks.map((chunk) => (
                      <div
                        key={chunk.id}
                        className={`px-3 py-1 rounded-full text-sm ${
                          chunk.isDefaultChunk
                            ? 'bg-gray-200 text-gray-600'
                            : 'bg-primary-green/10 text-primary-green'
                        }`}
                      >
                        {chunk.isDefaultChunk ? 'Default: ' : ''}
                        Pages {chunk.startPage}-{chunk.endPage}
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="font-medium text-lg">JSON Representation:</h3>
                    <button
                      onClick={() => {
                        // Create a blob with the JSON data
                        const jsonData = JSON.stringify(pdfChunks, null, 2);
                        const blob = new Blob([jsonData], { type: 'application/json' });

                        // Create a URL for the blob
                        const url = URL.createObjectURL(blob);

                        // Create a temporary link element
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = `${fileName.replace('.pdf', '')}_chunks.json`;

                        // Trigger the download
                        document.body.appendChild(link);
                        link.click();

                        // Clean up
                        document.body.removeChild(link);
                        URL.revokeObjectURL(url);
                      }}
                      className="px-3 py-1 bg-primary-green text-white rounded-md text-sm hover:bg-primary-green-dark transition-colors"
                    >
                      Download JSON
                    </button>
                  </div>
                  <div className="bg-gray-800 text-gray-100 p-4 rounded-md overflow-x-auto">
                    <pre className="text-sm">
                      {JSON.stringify(pdfChunks, null, 2)}
                    </pre>
                  </div>
                </div>
              </div>

              {/* Embeddings Display Component */}
              <EmbeddingsDisplay
                chunks={chunksWithEmbeddings || pdfChunks}
                isGenerating={isGeneratingEmbeddings}
                onGenerateEmbeddings={handleGenerateEmbeddings}
                prompt={prompt}
                onAnalyzePrompt={handleAnalyzePrompt}
                isAnalyzing={isAnalyzing}
                embeddingProgress={embeddingProgress}
              />

              {/* Analysis Results Component */}
              {analysisResults && (
                <>
                  <AnalysisResults results={analysisResults} prompt={prompt} />

                  {/* Link to Alternate Scenario Generator */}
                  <div className="bg-white rounded-lg shadow-md p-6 mb-8">
                    <h2 className="font-libre text-2xl font-bold text-text-primary mb-4">Generate Alternate Scenario</h2>
                    <p className="text-gray-600 mb-4">
                      Use the analysis results to generate an alternate timeline scenario using Google's Gemini AI.
                    </p>
                    <Link
                      href="/alternate-scenario"
                      className="inline-block px-4 py-2 bg-primary-green text-white rounded-md hover:bg-primary-green-dark transition-colors"
                    >
                      Create Alternate Scenario
                    </Link>
                  </div>
                </>
              )}
            </>
          )}
        </div>
      </main>
    </div>
  );
}