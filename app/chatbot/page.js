'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Chatbot from '../components/Chatbot';

export default function ChatbotPage() {
  const [isClient, setIsClient] = useState(false);
  const [chatbotStatus, setChatbotStatus] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    setIsClient(true);
    checkChatbotStatus();
  }, []);

  const checkChatbotStatus = async () => {
    try {
      const response = await fetch('/api/chatbot/chat');
      const data = await response.json();
      setChatbotStatus(data);
    } catch (error) {
      console.error('Error checking chatbot status:', error);
      setChatbotStatus({ success: false, error: 'Failed to connect to chatbot' });
    } finally {
      setIsLoading(false);
    }
  };

  if (!isClient) {
    return null;
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="bg-primary-green text-white p-4">
        <div className="container mx-auto flex justify-between items-center">
          <Link href="/" className="font-slackey text-2xl hover:text-gray-200 transition-colors">
            MoneyTales
          </Link>
          <nav className="flex space-x-4">
            <Link href="/" className="hover:text-gray-200 transition-colors">
              Home
            </Link>
            <Link href="/alternate-scenario" className="hover:text-gray-200 transition-colors">
              AI Scenarios
            </Link>
          </nav>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto py-8 px-4">
        <div className="max-w-4xl mx-auto">
          {/* Page Header */}
          <div className="text-center mb-8">
            <h1 className="font-libre text-4xl font-bold mb-4 text-text-primary">
              AI Assistant
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Get instant answers about MoneyTales features, PDF processing, AI scenario generation, and more. 
              Our AI assistant has been trained on our website content to help you quickly.
            </p>
          </div>

          {/* Status Check */}
          {isLoading ? (
            <div className="text-center py-8">
              <div className="inline-flex items-center space-x-2">
                <svg className="animate-spin h-5 w-5 text-primary-green" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>Connecting to AI assistant...</span>
              </div>
            </div>
          ) : chatbotStatus?.success ? (
            <>
              {/* Chatbot Interface */}
              <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
                <div className="h-[600px]">
                  <Chatbot />
                </div>
              </div>

              {/* Features and Capabilities */}
              <div className="grid md:grid-cols-2 gap-6 mb-8">
                <div className="bg-white rounded-lg shadow-md p-6">
                  <h3 className="font-libre text-xl font-bold mb-4 text-text-primary">
                    What I Can Help With
                  </h3>
                  <ul className="space-y-2 text-gray-600">
                    <li className="flex items-start space-x-2">
                      <svg className="w-5 h-5 text-primary-green mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      <span>Explain PDF processing and analysis features</span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <svg className="w-5 h-5 text-primary-green mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      <span>Guide you through AI scenario generation</span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <svg className="w-5 h-5 text-primary-green mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      <span>Answer questions about website features</span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <svg className="w-5 h-5 text-primary-green mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      <span>Help with navigation and usage</span>
                    </li>
                  </ul>
                </div>

                <div className="bg-white rounded-lg shadow-md p-6">
                  <h3 className="font-libre text-xl font-bold mb-4 text-text-primary">
                    Example Questions
                  </h3>
                  <div className="space-y-3">
                    <div className="bg-gray-50 rounded-lg p-3">
                      <p className="text-sm text-gray-700">"How do I upload and analyze a PDF?"</p>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-3">
                      <p className="text-sm text-gray-700">"What is alternate scenario generation?"</p>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-3">
                      <p className="text-sm text-gray-700">"How does the AI image generation work?"</p>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-3">
                      <p className="text-sm text-gray-700">"What technologies does MoneyTales use?"</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Additional Resources */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="font-libre text-xl font-bold mb-4 text-text-primary">
                  Quick Links
                </h3>
                <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  <Link 
                    href="/"
                    className="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 hover:border-primary-green hover:bg-green-50 transition-colors"
                  >
                    <svg className="w-6 h-6 text-primary-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <span className="font-medium">PDF Analysis</span>
                  </Link>
                  
                  <Link 
                    href="/alternate-scenario"
                    className="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 hover:border-primary-green hover:bg-green-50 transition-colors"
                  >
                    <svg className="w-6 h-6 text-primary-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                    <span className="font-medium">AI Scenarios</span>
                  </Link>
                  
                  <Link 
                    href="/game"
                    className="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 hover:border-primary-green hover:bg-green-50 transition-colors"
                  >
                    <svg className="w-6 h-6 text-primary-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.5a1.5 1.5 0 011.5 1.5v1a1.5 1.5 0 01-1.5 1.5H9m0-5a1.5 1.5 0 011.5-1.5H12m-3 7h3m-3 4h3m6-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="font-medium">Game Demo</span>
                  </Link>
                </div>
              </div>
            </>
          ) : (
            <div className="text-center py-12">
              <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
                <svg className="w-12 h-12 text-red-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <h3 className="text-lg font-semibold text-red-800 mb-2">
                  AI Assistant Unavailable
                </h3>
                <p className="text-red-600 mb-4">
                  {chatbotStatus?.error || 'Unable to connect to the AI assistant'}
                </p>
                <button
                  onClick={checkChatbotStatus}
                  className="btn btn-primary"
                >
                  Try Again
                </button>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
