import { cookies } from 'next/headers';
import { getTokens } from 'next-firebase-auth-edge/lib/next/tokens';
import { libreBaskerville, figTree, slackey } from './style_vars';
import "./globals.css";

export const metadata = {
  title: "Coming Soon - The Money Tales",
  description: "We are crafting something cool. Join our waiting list now.",
};

export default async function RootLayout({ children }) {

  return (
    <html lang="en">
      <body
        className={`${libreBaskerville.variable} ${slackey.variable} ${figTree.variable}`}
      >
        {children}
      </body>
    </html>
  );
}
