'use client';

import { useEffect, useRef, useState } from 'react';

export default function GamePage() {
  const canvasRef = useRef(null);
  const gameContainerRef = useRef(null);
  const [gameStatus, setGameStatus] = useState('loading');
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [statusMessage, setStatusMessage] = useState('Initializing game...');
  const [gameEngine, setGameEngine] = useState(null);
  const [isFullscreen, setIsFullscreen] = useState(false);

  useEffect(() => {
    let isComponentMounted = true;

    // Load the Godot engine script
    const loadGodotEngine = async () => {
      try {
        // Check if Engine is already loaded
        if (typeof window !== 'undefined' && window.Engine) {
          if (isComponentMounted) {
            initializeGame();
          }
          return;
        }

        // Create script element to load Godot engine
        const script = document.createElement('script');
        script.src = '/index.js';
        script.onload = () => {
          if (isComponentMounted) {
            initializeGame();
          }
        };
        script.onerror = () => {
          if (isComponentMounted) {
            setGameStatus('error');
            setStatusMessage('Failed to load game engine. Please check your internet connection and try again.');
          }
        };
        document.head.appendChild(script);
      } catch (error) {
        console.error('Error loading Godot engine:', error);
        if (isComponentMounted) {
          setGameStatus('error');
          setStatusMessage('Failed to initialize game');
        }
      }
    };

    const initializeGame = () => {
      if (typeof window === 'undefined' || !window.Engine) {
        setGameStatus('error');
        setStatusMessage('Game engine not available');
        return;
      }

      const GODOT_CONFIG = {
        args: [],
        canvasResizePolicy: 2,
        executable: "index",
        experimentalVK: false,
        fileSizes: { "index.pck": 88208, "index.wasm": 49282035 },
        focusCanvas: true,
        gdextensionLibs: [],
        serviceWorker: "index.service.worker.js"
      };

      const engine = new window.Engine(GODOT_CONFIG);
      setGameEngine(engine);

      // Check for missing features
      const missing = window.Engine.getMissingFeatures();
      if (missing.length !== 0) {
        const missingMsg = 'The following features required to run the game are missing:\n' + missing.join('\n');
        setGameStatus('error');
        setStatusMessage(missingMsg);
        return;
      }

      // Start the game
      setGameStatus('loading');
      setStatusMessage('Loading game assets...');

      engine.startGame({
        canvas: canvasRef.current,
        onProgress: (current, total) => {
          if (total > 0) {
            const progress = (current / total) * 100;
            setLoadingProgress(progress);
            setStatusMessage(`Loading... ${Math.round(progress)}%`);

            if (current === total) {
              setTimeout(() => {
                setGameStatus('running');
                setStatusMessage('');
              }, 500);
            }
          }
        },
      }).then(() => {
        setGameStatus('running');
        setStatusMessage('');
      }).catch((error) => {
        console.error('Game initialization error:', error);
        setGameStatus('error');
        setStatusMessage('Failed to start game: ' + (error.message || error));
      });
    };

    loadGodotEngine();

    // Cleanup function
    return () => {
      isComponentMounted = false;
      if (gameEngine) {
        // Clean up game engine if needed
        try {
          gameEngine.requestQuit();
        } catch (error) {
          console.warn('Error during game cleanup:', error);
        }
      }
    };
  }, [gameEngine]);

  // Fullscreen functionality
  const toggleFullscreen = async () => {
    if (!gameContainerRef.current) return;

    try {
      if (!document.fullscreenElement) {
        await gameContainerRef.current.requestFullscreen();
        setIsFullscreen(true);
      } else {
        await document.exitFullscreen();
        setIsFullscreen(false);
      }
    } catch (error) {
      console.warn('Fullscreen toggle failed:', error);
    }
  };

  // Listen for fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  const renderLoadingIndicator = () => {
    if (gameStatus === 'loading') {
      return (
        <div className="absolute inset-0 flex flex-col items-center justify-center bg-black bg-opacity-75 z-10">
          <div className="text-white text-center">
            <div className="mb-4">
              <div className="w-64 h-2 bg-gray-700 rounded-full overflow-hidden">
                <div
                  className="h-full bg-green-500 transition-all duration-300 ease-out"
                  style={{ width: `${loadingProgress}%` }}
                />
              </div>
            </div>
            <p className="text-sm">{statusMessage}</p>
          </div>
        </div>
      );
    }

    if (gameStatus === 'error') {
      return (
        <div className="absolute inset-0 flex flex-col items-center justify-center bg-black bg-opacity-75 z-10">
          <div className="text-white text-center max-w-md px-4">
            <div className="mb-4">
              <svg className="w-16 h-16 mx-auto text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold mb-2">Game Loading Error</h3>
            <p className="text-sm text-gray-300 whitespace-pre-line">{statusMessage}</p>
            <button
              onClick={() => window.location.reload()}
              className="mt-4 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      );
    }

    return null;
  };

  return (
    <div className="min-h-screen bg-black flex flex-col">
      {/* Header */}
      <header className="bg-[var(--primary-green)] text-white p-4 relative z-30">
        <div className="container mx-auto flex items-center justify-between">
          <h1 className="text-2xl font-bold font-slackey">The Money Tales</h1>
          <nav className="flex space-x-4">
            <a href="/" className="hover:text-green-200 transition-colors">Home</a>
            <a href="/game" className="text-green-200">Game</a>
          </nav>
        </div>

        {/* Game Status Indicator */}
        {gameStatus !== 'running' && (
          <div className="absolute top-full left-0 right-0 bg-yellow-600 text-white text-center py-1 text-sm">
            {gameStatus === 'loading' ? 'Loading Game...' : gameStatus === 'error' ? 'Game Error' : 'Initializing...'}
          </div>
        )}
      </header>

      {/* Game Container */}
      <main className="flex-1 relative overflow-hidden">
        <div
          ref={gameContainerRef}
          className="w-full h-full relative"
          style={{ minHeight: 'calc(100vh - 120px)' }}
        >
          {/* Game Controls */}
          {gameStatus === 'running' && (
            <div className="absolute top-4 right-4 z-20 flex space-x-2">
              <button
                onClick={toggleFullscreen}
                className="bg-black bg-opacity-50 hover:bg-opacity-75 text-white p-2 rounded-md transition-all"
                title={isFullscreen ? 'Exit Fullscreen' : 'Enter Fullscreen'}
              >
                {isFullscreen ? (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                ) : (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                  </svg>
                )}
              </button>
            </div>
          )}

          <canvas
            ref={canvasRef}
            id="game-canvas"
            className="block w-full h-full bg-black"
            style={{
              display: 'block',
              margin: 0,
              color: 'white',
              touchAction: 'none',
              outline: 'none'
            }}
            tabIndex={0}
          >
            HTML5 canvas appears to be unsupported in the current browser.
            <br />
            Please try updating or use a different browser.
          </canvas>

          {renderLoadingIndicator()}
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white p-4 text-center">
        <p className="text-sm">
          © 2024 The Money Tales. Experience financial literacy through interactive storytelling.
        </p>
      </footer>
    </div>
  );
}