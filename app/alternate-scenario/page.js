'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { generateAlternateScenario } from '../utils/geminiService';
import { extractBookInformation } from '../utils/embeddingAnalysis';
import AlternateScenarioDisplay from '../components/AlternateScenarioDisplay';

export default function AlternateScenarioPage() {
  const [isGenerating, setIsGenerating] = useState(true);
  const [generatedScenario, setGeneratedScenario] = useState(null);
  const [error, setError] = useState('');
  // We store the analysis results but don't directly use them in rendering
  const [, setAnalysisResults] = useState(null);
  const [userPrompt, setUserPrompt] = useState('');
  const [bookInfo, setBookInfo] = useState({
    bookTitle: '',
    author: '',
    changeLocation: '',
    originalEvent: ''
  });

  // Check if we're in the browser
  const [isClient, setIsClient] = useState(false);
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Load data from localStorage and generate scenario automatically
  useEffect(() => {
    if (!isClient) return;

    async function loadDataAndGenerateScenario() {
      try {
        // Load analysis results and user prompt from localStorage
        const savedResults = localStorage.getItem('analysisResults');
        const savedPrompt = localStorage.getItem('userPrompt');

        if (!savedResults || !savedPrompt) {
          setError('No analysis results or prompt found. Please analyze a PDF first.');
          setIsGenerating(false);
          return;
        }

        const parsedResults = JSON.parse(savedResults);
        setAnalysisResults(parsedResults);
        setUserPrompt(savedPrompt);

        // Extract book information from analysis results
        const extractedBookInfo = extractBookInformation(parsedResults);
        setBookInfo(extractedBookInfo);

        // Generate the alternate scenario automatically
        await generateScenario(extractedBookInfo, savedPrompt);
      } catch (error) {
        console.error('Error loading data and generating scenario:', error);
        setError('Error loading data: ' + (error.message || 'Unknown error'));
        setIsGenerating(false);
      }
    }

    loadDataAndGenerateScenario();
  }, [isClient]);

  // Function to generate the scenario
  const generateScenario = async (bookData, whatIfPrompt) => {
    try {
      // Ensure we have the required data
      if (!bookData.bookTitle || !bookData.author || !whatIfPrompt) {
        setError('Missing required information to generate scenario.');
        setIsGenerating(false);
        return;
      }

      // Generate the scenario
      const result = await generateAlternateScenario({
        bookTitle: bookData.bookTitle,
        author: bookData.author,
        changeLocation: bookData.changeLocation,
        whatIfPrompt: whatIfPrompt
      });

      if (result.success) {
        setGeneratedScenario(result);
      } else {
        setError(result.error || 'Failed to generate scenario');
      }
    } catch (error) {
      console.error('Error generating scenario:', error);
      setError(error.message || 'An unexpected error occurred');
    } finally {
      setIsGenerating(false);
    }
  };

  if (!isClient) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <main className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Alternate Timeline Scenario</h1>
          <Link
            href="/"
            className="px-4 py-2 bg-gray-700 text-white rounded-md hover:bg-gray-600 transition-colors"
          >
            Back to Analysis
          </Link>
        </div>

        {/* Book and Prompt Information */}
        {!isGenerating && !error && (
          <div className="bg-gray-800 p-6 rounded-lg shadow-lg mb-8">
            <h2 className="text-xl font-semibold mb-4">Scenario Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <p className="text-gray-400 text-sm">Book Title</p>
                <p className="text-white">{bookInfo.bookTitle}</p>
              </div>
              <div>
                <p className="text-gray-400 text-sm">Author</p>
                <p className="text-white">{bookInfo.author}</p>
              </div>
              {bookInfo.changeLocation && (
                <div>
                  <p className="text-gray-400 text-sm">Chapter/Section</p>
                  <p className="text-white">{bookInfo.changeLocation}</p>
                </div>
              )}
            </div>
            <div className="mb-4">
              <p className="text-gray-400 text-sm">Original Event</p>
              <p className="text-white bg-gray-700/50 p-3 rounded-md mt-1">{bookInfo.originalEvent}</p>
            </div>
            <div>
              <p className="text-gray-400 text-sm">What If Prompt</p>
              <p className="text-white bg-gray-700/50 p-3 rounded-md mt-1">{userPrompt}</p>
            </div>
          </div>
        )}

        {/* Generated Scenario Display */}
        <AlternateScenarioDisplay
          scenario={generatedScenario}
          isLoading={isGenerating}
          error={error}
        />

        {/* Error with option to go back */}
        {error && (
          <div className="mt-6 text-center">
            <Link
              href="/"
              className="px-6 py-3 bg-primary-green text-white rounded-md hover:bg-primary-green-dark transition-colors"
            >
              Return to PDF Analysis
            </Link>
          </div>
        )}
      </main>
    </div>
  );
}
