'use client';

import { useState } from 'react';
import Image from 'next/image';
import LoginComponent from '../components/LoginComponent';
import SignupComponent from '../components/SignupComponent';

export default function AuthPage() {
  const [view, setView] = useState('choice');

  const handleBack = (screen) => {
    setView(screen);
  };

  return (
    <div className="flex flex-col md:flex-row h-[100dvh] md:min-h-screen bg-[#FFF4E0] overflow-hidden">
      {/* Left Panel - Desktop Only */}
      <div className="hidden md:flex md:w-1/2 bg-[var(--primary-green)] p-8 flex-col justify-between">
        <a href="/" className="flex items-center space-x-2">
          <div className="w-32 h-12 font-slackey text-4xl text-white">
            MoneyTales
          </div>
        </a>

        <div className="space-y-6">
          <div className="flex justify-center">
            <div className="relative w-96 h-96">
              <Image
                src="/coin_mascot.svg"
                alt="Happy coin mascot"
                fill
                className="object-contain"
              />
            </div>
          </div>
          <div className="font-libre text-5xl font-bold text-white text-center mx-auto hidden lg:block">
            One Step closer to Financial Freedom
          </div>
        </div>
        <div className="h-8" />
      </div>

      {/* Mobile Top Section - Only shown in choice view */}
      {view === 'choice' && (
        <div className="md:hidden">
          <div className="bg-green-700 p-4 flex flex-col items-center pb-24 relative">
            <div className="font-slackey text-3xl text-white mb-4">
              MoneyTales
            </div>
            <div className="absolute top-14 w-44 h-44">
              <Image
                src="/coin_mascot_inverted.svg"
                alt="Happy coin mascot"
                fill
                className="object-contain"
              />
            </div>
          </div>
          <div className="font-libre text-2xl font-bold text-black max-w-64 mx-auto text-center mt-20 mb-2">
            One Step closer to Financial Freedom
          </div>
        </div>
      )}

      {/* Right Panel */}
      {view === 'choice' ? (
        <div className="w-full md:w-1/2 p-3 md:p-8 flex items-center justify-center bg-[#FFF4E0] flex-grow">
          <div className="w-full max-w-md space-y-3 md:space-y-8">
            {/* New User Section */}
            <div className="text-center space-y-3 md:space-y-4 border-2 border-dotted border-gray-400 p-4 md:p-8 rounded-lg bg-[#FFF4E0] md:bg-opacity-20 hover:bg-opacity-20 hover:bg-[#13824B] h-36 md:h-64 flex flex-col justify-center items-center">
              <h2 className="text-2xl md:text-3xl font-bold font-libre text-black">New here ?</h2>
              <div className="flex justify-center">
                <button
                  onClick={() => setView('signup')}
                  className="py-2 md:py-3 px-6 md:px-8 rounded-lg font-bold bg-[var(--primary-green)] text-white hover:bg-[#0f6b3d] transition-colors duration-200 min-w-[120px] md:min-w-[150px] text-base md:text-lg"
                >
                  Sign up
                </button>
              </div>
            </div>

            {/* Existing User Section */}
            <div className="text-center space-y-3 md:space-y-4 border-2 border-dotted border-gray-400 p-4 md:p-8 rounded-lg bg-[#FFF4E0] hover:bg-opacity-20 hover:bg-[#13824B] h-36 md:h-64 flex flex-col justify-center items-center">
              <h2 className="text-2xl md:text-3xl font-bold font-libre text-black">Got an Account already</h2>
              <div className="flex justify-center">
                <button
                  onClick={() => setView('login')}
                  className="py-2 md:py-3 px-6 md:px-8 rounded-lg font-bold bg-[var(--primary-green)] text-white hover:bg-[#0f6b3d] transition-colors duration-200 min-w-[120px] md:min-w-[150px] text-base md:text-lg"
                >
                  Log in
                </button>
              </div>
            </div>
          </div>
        </div>
      ) : view === 'login' ? (
        <LoginComponent onBack={handleBack} />
      ) : (
        <SignupComponent onBack={handleBack} />
      )}
    </div>
  );
}