import "../globals.css";
import localFont from 'next/font/local';
import { libreBaskerville, figTree, slackey } from '../style_vars';

export const metadata = {
  title: "Coming Soon - The Money Tales",
  description: "We are crafting something cool. Join our waiting list now.",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body
        className={`${libreBaskerville.variable} ${slackey.variable} ${figTree.variable} bg-[#FFF4E0]`}
      >
    
        {children}
 

      </body>
    </html>
  );
}
