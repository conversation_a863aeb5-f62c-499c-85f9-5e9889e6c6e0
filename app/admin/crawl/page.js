'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

export default function CrawlAdminPage() {
  const [isClient, setIsClient] = useState(false);
  const [crawlStatus, setCrawlStatus] = useState(null);
  const [isCrawling, setIsCrawling] = useState(false);
  const [crawlResults, setCrawlResults] = useState(null);
  const [error, setError] = useState('');

  // Crawl configuration
  const [startUrl, setStartUrl] = useState('');
  const [maxPages, setMaxPages] = useState(30);
  const [maxDepth, setMaxDepth] = useState(2);
  const [delay, setDelay] = useState(1000);

  useEffect(() => {
    setIsClient(true);
    // Set default URL to current domain
    if (typeof window !== 'undefined') {
      setStartUrl(window.location.origin);
    }
    loadCrawlStatus();
  }, []);

  const loadCrawlStatus = async () => {
    try {
      const response = await fetch('/api/chatbot/crawl');
      const data = await response.json();
      setCrawlStatus(data);
    } catch (error) {
      console.error('Error loading crawl status:', error);
      setError('Failed to load crawl status');
    }
  };

  const startCrawl = async () => {
    if (!startUrl.trim()) {
      setError('Please enter a start URL');
      return;
    }

    setIsCrawling(true);
    setError('');
    setCrawlResults(null);

    try {
      const response = await fetch('/api/chatbot/crawl', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          startUrl: startUrl.trim(),
          options: {
            maxPages: parseInt(maxPages),
            maxDepth: parseInt(maxDepth),
            delay: parseInt(delay)
          }
        }),
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        throw new Error(data.error || 'Crawling failed');
      }

      setCrawlResults(data);
      await loadCrawlStatus(); // Refresh status

    } catch (error) {
      console.error('Crawl error:', error);
      setError(error.message);
    } finally {
      setIsCrawling(false);
    }
  };

  if (!isClient) {
    return null;
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="bg-primary-green text-white p-4">
        <div className="container mx-auto flex justify-between items-center">
          <Link href="/" className="font-slackey text-2xl hover:text-gray-200 transition-colors">
            MoneyTales
          </Link>
          <nav className="flex space-x-4">
            <Link href="/" className="hover:text-gray-200 transition-colors">
              Home
            </Link>
            <Link href="/alternate-scenario" className="hover:text-gray-200 transition-colors">
              AI Scenarios
            </Link>
          </nav>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto py-8 px-4">
        <div className="max-w-4xl mx-auto">
          {/* Page Header */}
          <div className="mb-8">
            <h1 className="font-libre text-3xl font-bold mb-4 text-text-primary">
              Website Crawler Admin
            </h1>
            <p className="text-gray-600">
              Crawl and index website content to train the AI chatbot. This will analyze your website pages and create embeddings for intelligent responses.
            </p>
          </div>

          {/* Current Status */}
          {crawlStatus && (
            <div className="bg-white rounded-lg shadow-md p-6 mb-8">
              <h2 className="font-libre text-xl font-bold mb-4 text-text-primary">
                Current Status
              </h2>
              <div className="grid md:grid-cols-3 gap-4">
                <div className="bg-blue-50 rounded-lg p-4">
                  <h3 className="font-semibold text-blue-800">Total Pages</h3>
                  <p className="text-2xl font-bold text-blue-600">
                    {crawlStatus.stats?.totalPages || 0}
                  </p>
                </div>
                <div className="bg-green-50 rounded-lg p-4">
                  <h3 className="font-semibold text-green-800">Content Chunks</h3>
                  <p className="text-2xl font-bold text-green-600">
                    {crawlStatus.stats?.totalChunks || 0}
                  </p>
                </div>
                <div className="bg-purple-50 rounded-lg p-4">
                  <h3 className="font-semibold text-purple-800">Last Crawl</h3>
                  <p className="text-sm text-purple-600">
                    {crawlStatus.stats?.lastCrawl
                      ? new Date(crawlStatus.stats.lastCrawl).toLocaleDateString()
                      : 'Never'
                    }
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Crawl Configuration */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="font-libre text-xl font-bold mb-4 text-text-primary">
              Crawl Configuration
            </h2>

            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Start URL
                </label>
                <input
                  type="url"
                  value={startUrl}
                  onChange={(e) => setStartUrl(e.target.value)}
                  placeholder="https://example.com"
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent"
                  disabled={isCrawling}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Max Pages
                </label>
                <input
                  type="number"
                  value={maxPages}
                  onChange={(e) => setMaxPages(e.target.value)}
                  min="1"
                  max="100"
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent"
                  disabled={isCrawling}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Max Depth
                </label>
                <input
                  type="number"
                  value={maxDepth}
                  onChange={(e) => setMaxDepth(e.target.value)}
                  min="1"
                  max="5"
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent"
                  disabled={isCrawling}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Delay (ms)
                </label>
                <input
                  type="number"
                  value={delay}
                  onChange={(e) => setDelay(e.target.value)}
                  min="500"
                  max="5000"
                  step="500"
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent"
                  disabled={isCrawling}
                />
              </div>
            </div>

            <div className="mt-6">
              <button
                onClick={startCrawl}
                disabled={isCrawling || !startUrl.trim()}
                className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                  isCrawling || !startUrl.trim()
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-primary-green text-white hover:bg-primary-green-dark'
                }`}
              >
                {isCrawling ? (
                  <span className="flex items-center space-x-2">
                    <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span>Crawling...</span>
                  </span>
                ) : (
                  'Start Crawl'
                )}
              </button>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-8">
              <div className="flex items-center space-x-2">
                <svg className="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <span className="text-red-800 font-medium">Error:</span>
                <span className="text-red-700">{error}</span>
              </div>
            </div>
          )}

          {/* Crawl Results */}
          {crawlResults && (
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="font-libre text-xl font-bold mb-4 text-text-primary">
                Crawl Results
              </h2>

              <div className="grid md:grid-cols-3 gap-4 mb-6">
                <div className="bg-blue-50 rounded-lg p-4">
                  <h3 className="font-semibold text-blue-800">Pages Crawled</h3>
                  <p className="text-2xl font-bold text-blue-600">
                    {crawlResults.results?.crawled || 0}
                  </p>
                </div>
                <div className="bg-green-50 rounded-lg p-4">
                  <h3 className="font-semibold text-green-800">Pages Indexed</h3>
                  <p className="text-2xl font-bold text-green-600">
                    {crawlResults.results?.indexed || 0}
                  </p>
                </div>
                <div className="bg-red-50 rounded-lg p-4">
                  <h3 className="font-semibold text-red-800">Errors</h3>
                  <p className="text-2xl font-bold text-red-600">
                    {crawlResults.results?.errors || 0}
                  </p>
                </div>
              </div>

              <div className="text-center">
                <p className="text-green-600 font-medium">
                  ✅ Crawling completed! The AI assistant widget is now ready to answer questions about your website.
                </p>
                <p className="text-sm text-gray-600 mt-2">
                  Look for the chat icon in the bottom-right corner of any page.
                </p>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
