@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Color System */
  --primary-green: #13824B;
  --primary-green-light: #1A9D5C;
  --primary-green-dark: #0F6B3D;

  --secondary-green: #13824B;

  --cream: #FFF4E0;
  --cream-light: #FFF9ED;
  --cream-dark: #F5E8D0;

  --black: #1A1A1A;
  --white: #FFFFFF;

  --gray-100: #F5F5F5;
  --gray-200: #E5E5E5;
  --gray-300: #D4D4D4;
  --gray-400: #A3A3A3;
  --gray-500: #737373;
  --gray-600: #525252;
  --gray-700: #404040;
  --gray-800: #262626;
  --gray-900: #171717;

  --success: #22C55E;
  --warning: #F59E0B;
  --error: #EF4444;
  --info: #3B82F6;

  /* Spacing System */
  --space-xs: 0.25rem;  /* 4px */
  --space-sm: 0.5rem;   /* 8px */
  --space-md: 1rem;     /* 16px */
  --space-lg: 1.5rem;   /* 24px */
  --space-xl: 2rem;     /* 32px */
  --space-2xl: 3rem;    /* 48px */
  --space-3xl: 4rem;    /* 64px */

  /* Border Radius */
  --radius-sm: 0.25rem; /* 4px */
  --radius-md: 0.5rem;  /* 8px */
  --radius-lg: 1rem;    /* 16px */
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* Transitions */
  --transition-fast: 150ms ease;
  --transition-normal: 300ms ease;
  --transition-slow: 500ms ease;

  /* Default Theme */
  --background: var(--cream);
  --foreground: var(--black);
  --text-primary: var(--primary-green);
  --text-secondary: var(--gray-700);

  /* Z-index layers */
  --z-base: 1;
  --z-dropdown: 10;
  --z-sticky: 100;
  --z-fixed: 1000;
  --z-modal: 1001;
  --z-popover: 1010;
  --z-tooltip: 1100;
}

/* Light Theme (default) */
.light-theme {
  --background: var(--cream);
  --foreground: var(--black);
  --text-primary: var(--primary-green);
  --text-secondary: var(--gray-700);
  --card-bg: var(--white);
  --card-border: var(--gray-200);
  --input-bg: var(--white);
  --input-border: var(--gray-300);
  --input-text: var(--black);
  --button-primary-bg: var(--primary-green);
  --button-primary-text: var(--white);
}

/* Dark Theme */
.dark-theme {
  --background: var(--gray-900);
  --foreground: var(--cream);
  --text-primary: var(--primary-green-light);
  --text-secondary: var(--gray-300);
  --card-bg: var(--gray-800);
  --card-border: var(--gray-700);
  --input-bg: var(--gray-800);
  --input-border: var(--gray-600);
  --input-text: var(--white);
  --button-primary-bg: var(--primary-green-light);
  --button-primary-text: var(--white);
}

body {
  color: var(--foreground);
  background: var(--background);
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Hide scrollbar for Chrome, Safari and Opera */
.no-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.no-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

/* Common Component Styles */
@layer components {
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-all duration-300;
  }

  .btn-primary {
    @apply bg-[var(--primary-green)] text-white hover:bg-[var(--primary-green-dark)];
  }

  .btn-secondary {
    @apply bg-transparent border border-[var(--primary-green)] text-[var(--primary-green)]
           hover:bg-[var(--primary-green)] hover:text-white;
  }

  .card {
    @apply bg-white rounded-lg shadow-md p-6;
  }

  .input {
    @apply w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none
           focus:ring-2 focus:ring-[var(--primary-green)] focus:border-transparent;
  }
}