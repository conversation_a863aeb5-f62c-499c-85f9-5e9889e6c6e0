/**
 * Test script for the chatbot functionality
 * Run with: node test-chatbot.js
 */

import dotenv from 'dotenv';
import { crawlAndIndexWebsite, processChatQuery } from './lib/chatbotService.js';

// Load environment variables
dotenv.config({ path: '.env.local' });

async function testChatbot() {
  console.log('🤖 Testing MoneyTales Chatbot');
  console.log('================================\n');

  try {
    // Test 1: Check if we can process a simple query (without crawling first)
    console.log('📝 Test 1: Simple query without indexed content');
    const simpleQuery = "What is MoneyTales?";
    const simpleResult = await processChatQuery(simpleQuery);
    
    if (simpleResult.success) {
      console.log('✅ Query processed successfully');
      console.log('Response:', simpleResult.response.substring(0, 100) + '...');
      console.log('Sources found:', simpleResult.sources?.length || 0);
    } else {
      console.log('❌ Query failed:', simpleResult.error);
    }
    
    console.log('\n' + '='.repeat(50) + '\n');

    // Test 2: Crawl a sample website (using localhost for testing)
    console.log('🕷️  Test 2: Crawling website content');
    const testUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
    console.log(`Crawling: ${testUrl}`);
    
    const crawlResult = await crawlAndIndexWebsite(testUrl, {
      maxPages: 5,
      delay: 500,
      maxDepth: 1
    });
    
    if (crawlResult.success) {
      console.log('✅ Crawling completed successfully');
      console.log(`Pages crawled: ${crawlResult.crawled}`);
      console.log(`Pages indexed: ${crawlResult.indexed}`);
      console.log(`Errors: ${crawlResult.errors}`);
    } else {
      console.log('❌ Crawling failed:', crawlResult.error);
    }
    
    console.log('\n' + '='.repeat(50) + '\n');

    // Test 3: Query with indexed content
    console.log('💬 Test 3: Query with indexed content');
    const indexedQuery = "How do I upload a PDF?";
    const indexedResult = await processChatQuery(indexedQuery);
    
    if (indexedResult.success) {
      console.log('✅ Query with indexed content successful');
      console.log('Response:', indexedResult.response.substring(0, 200) + '...');
      console.log('Sources found:', indexedResult.sources?.length || 0);
      console.log('Relevant chunks:', indexedResult.relevantChunks || 0);
    } else {
      console.log('❌ Query with indexed content failed:', indexedResult.error);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 4: Multiple queries to test different topics
    console.log('🔄 Test 4: Multiple topic queries');
    const testQueries = [
      "What is alternate scenario generation?",
      "How does PDF processing work?",
      "What AI technologies are used?",
      "How do I get started?"
    ];

    for (const query of testQueries) {
      console.log(`\nQuery: "${query}"`);
      const result = await processChatQuery(query);
      
      if (result.success) {
        console.log(`✅ Response length: ${result.response.length} chars`);
        console.log(`📚 Sources: ${result.sources?.length || 0}`);
      } else {
        console.log(`❌ Failed: ${result.error}`);
      }
    }

  } catch (error) {
    console.error('💥 Test failed with error:', error);
  }

  console.log('\n🏁 Chatbot testing completed!');
}

// Run the test
testChatbot().catch(console.error);
