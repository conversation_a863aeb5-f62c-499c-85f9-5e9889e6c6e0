/**
 * Chatbot Service
 * Handles AI-powered responses based on website content
 */

import { GoogleGenAI } from '@google/genai';
import { searchRelevantContent, searchRelevantContentEnhanced } from './vectorStore.js';
import { crawlWebsite } from './crawler.js';
import { storePageContent } from './vectorStore.js';

/**
 * Generate AI response based on user query and relevant content
 */
export async function generateChatbotResponse(userQuery, relevantContent = []) {
  try {
    const apiKey = process.env.GOOGLE_API_KEY;
    if (!apiKey) {
      throw new Error('Google API key not configured');
    }

    const genAI = new GoogleGenAI({ apiKey });

    // Prepare context from relevant content
    const context = relevantContent
      .map(chunk => `Source: ${chunk.url}\nContent: ${chunk.content}`)
      .join('\n\n---\n\n');

    const systemPrompt = `You are a helpful AI assistant for the MoneyTales website. Your role is to answer questions about the website's content, features, and services based on the provided context.

Guidelines:
- Answer questions directly and helpfully
- Use the provided context to give accurate information
- If you don't have enough information in the context, say so politely
- Keep responses conversational but informative
- Reference specific pages or sections when relevant
- If asked about features not in the context, explain what you know about MoneyTales generally

Context from website:
${context}

User Question: ${userQuery}

Please provide a helpful response based on the context above.`;

    const result = await genAI.models.generateContent({
      model: 'gemini-2.0-flash-001',
      contents: systemPrompt,
      config: {
        temperature: 0.7,
        maxOutputTokens: 1000,
      },
    });

    const response = result.response?.candidates?.[0]?.content?.parts?.[0]?.text;

    if (!response) {
      throw new Error('No response generated from AI');
    }

    return {
      success: true,
      response: response.trim(),
      sources: relevantContent.map(chunk => ({
        url: chunk.url,
        title: chunk.title || 'Untitled',
        snippet: chunk.content.substring(0, 150) + '...'
      }))
    };

  } catch (error) {
    console.error('Error generating chatbot response:', error);
    return {
      success: false,
      error: error.message || 'Failed to generate response'
    };
  }
}

/**
 * Process user query and generate response
 */
export async function processChatQuery(userQuery) {
  try {
    console.log(`Processing chat query: "${userQuery}"`);

    // Search for relevant content using enhanced search
    const relevantContent = await searchRelevantContentEnhanced(userQuery, 5);

    console.log(`Found ${relevantContent.length} relevant content chunks`);

    // Generate AI response
    const aiResponse = await generateChatbotResponse(userQuery, relevantContent);

    if (!aiResponse.success) {
      throw new Error(aiResponse.error);
    }

    return {
      success: true,
      response: aiResponse.response,
      sources: aiResponse.sources,
      relevantChunks: relevantContent.length
    };

  } catch (error) {
    console.error('Error processing chat query:', error);

    // Fallback response
    return {
      success: false,
      response: "I'm sorry, I'm having trouble accessing the website information right now. Please try again later or contact support if the issue persists.",
      error: error.message,
      sources: []
    };
  }
}

/**
 * Crawl and index website content
 */
export async function crawlAndIndexWebsite(startUrl, options = {}) {
  try {
    console.log(`Starting website crawl from: ${startUrl}`);

    const defaultOptions = {
      maxPages: 50,
      delay: 1000,
      maxDepth: 3,
      excludePatterns: [
        '/api/',
        '/admin/',
        '/_next/',
        '/static/',
        '/images/',
        '/css/',
        '/js/',
        '.pdf',
        '.jpg',
        '.png',
        '.gif'
      ]
    };

    const crawlOptions = { ...defaultOptions, ...options };

    // Crawl the website
    const crawlResults = await crawlWebsite(startUrl, crawlOptions);

    console.log(`Crawled ${crawlResults.length} pages`);

    // Store content with embeddings
    const indexResults = [];
    let successCount = 0;
    let errorCount = 0;

    for (const pageData of crawlResults) {
      if (pageData.success && pageData.content.trim().length > 100) {
        try {
          await storePageContent(pageData);
          indexResults.push({
            url: pageData.url,
            status: 'indexed',
            chunkCount: pageData.chunks.length
          });
          successCount++;
          console.log(`Indexed: ${pageData.url}`);
        } catch (error) {
          console.error(`Error indexing ${pageData.url}:`, error);
          indexResults.push({
            url: pageData.url,
            status: 'error',
            error: error.message
          });
          errorCount++;
        }
      } else {
        indexResults.push({
          url: pageData.url,
          status: 'skipped',
          reason: pageData.success ? 'Content too short' : pageData.error
        });
      }
    }

    return {
      success: true,
      crawled: crawlResults.length,
      indexed: successCount,
      errors: errorCount,
      results: indexResults,
      startUrl,
      completedAt: new Date().toISOString()
    };

  } catch (error) {
    console.error('Error crawling and indexing website:', error);
    return {
      success: false,
      error: error.message,
      startUrl,
      completedAt: new Date().toISOString()
    };
  }
}

/**
 * Get chatbot statistics
 */
export async function getChatbotStats() {
  try {
    // This would typically query your database for stats
    // For now, return basic info
    return {
      success: true,
      stats: {
        totalPages: 0, // Would be fetched from database
        totalChunks: 0, // Would be fetched from database
        lastCrawl: null, // Would be fetched from database
        status: 'ready'
      }
    };
  } catch (error) {
    console.error('Error getting chatbot stats:', error);
    return {
      success: false,
      error: error.message
    };
  }
}
