/**
 * Website Crawler Utilities
 * Crawls and extracts content from website pages
 */

import { JSDOM } from 'jsdom';

/**
 * Extract text content from HTML
 */
export function extractTextFromHTML(html) {
  try {
    const dom = new JSDOM(html);
    const document = dom.window.document;
    
    // Remove script and style elements
    const scripts = document.querySelectorAll('script, style, nav, footer');
    scripts.forEach(el => el.remove());
    
    // Extract main content areas
    const contentSelectors = [
      'main',
      '[role="main"]',
      '.content',
      '#content',
      'article',
      '.post',
      '.page-content'
    ];
    
    let mainContent = '';
    for (const selector of contentSelectors) {
      const element = document.querySelector(selector);
      if (element) {
        mainContent = element.textContent || '';
        break;
      }
    }
    
    // Fallback to body if no main content found
    if (!mainContent) {
      mainContent = document.body?.textContent || '';
    }
    
    // Clean up the text
    return mainContent
      .replace(/\s+/g, ' ')
      .replace(/\n+/g, '\n')
      .trim();
  } catch (error) {
    console.error('Error extracting text from HTML:', error);
    return '';
  }
}

/**
 * Extract metadata from HTML
 */
export function extractMetadata(html, url) {
  try {
    const dom = new JSDOM(html);
    const document = dom.window.document;
    
    const title = document.querySelector('title')?.textContent || '';
    const description = document.querySelector('meta[name="description"]')?.getAttribute('content') || '';
    const keywords = document.querySelector('meta[name="keywords"]')?.getAttribute('content') || '';
    
    // Extract headings for structure
    const headings = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6'))
      .map(h => ({
        level: parseInt(h.tagName.charAt(1)),
        text: h.textContent?.trim() || ''
      }))
      .filter(h => h.text);
    
    return {
      url,
      title: title.trim(),
      description: description.trim(),
      keywords: keywords.trim(),
      headings,
      lastCrawled: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error extracting metadata:', error);
    return {
      url,
      title: '',
      description: '',
      keywords: '',
      headings: [],
      lastCrawled: new Date().toISOString()
    };
  }
}

/**
 * Chunk text into smaller pieces for better embedding
 */
export function chunkText(text, maxChunkSize = 1000, overlap = 200) {
  if (!text || text.length <= maxChunkSize) {
    return [text];
  }
  
  const chunks = [];
  let start = 0;
  
  while (start < text.length) {
    let end = start + maxChunkSize;
    
    // Try to break at sentence boundaries
    if (end < text.length) {
      const lastPeriod = text.lastIndexOf('.', end);
      const lastNewline = text.lastIndexOf('\n', end);
      const breakPoint = Math.max(lastPeriod, lastNewline);
      
      if (breakPoint > start + maxChunkSize * 0.5) {
        end = breakPoint + 1;
      }
    }
    
    chunks.push(text.slice(start, end).trim());
    start = end - overlap;
  }
  
  return chunks.filter(chunk => chunk.length > 50); // Filter out very small chunks
}

/**
 * Get internal links from a page
 */
export function extractInternalLinks(html, baseUrl) {
  try {
    const dom = new JSDOM(html);
    const document = dom.window.document;
    const links = Array.from(document.querySelectorAll('a[href]'));
    
    const baseUrlObj = new URL(baseUrl);
    const internalLinks = new Set();
    
    links.forEach(link => {
      try {
        const href = link.getAttribute('href');
        if (!href) return;
        
        // Resolve relative URLs
        const absoluteUrl = new URL(href, baseUrl);
        
        // Check if it's an internal link
        if (absoluteUrl.hostname === baseUrlObj.hostname) {
          // Normalize the URL (remove fragments, trailing slashes)
          const normalizedUrl = `${absoluteUrl.protocol}//${absoluteUrl.hostname}${absoluteUrl.pathname}`;
          internalLinks.add(normalizedUrl.replace(/\/$/, '') || '/');
        }
      } catch (error) {
        // Invalid URL, skip
      }
    });
    
    return Array.from(internalLinks);
  } catch (error) {
    console.error('Error extracting internal links:', error);
    return [];
  }
}

/**
 * Crawl a single page
 */
export async function crawlPage(url) {
  try {
    console.log(`Crawling: ${url}`);
    
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'MoneyTales-Chatbot-Crawler/1.0'
      }
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const html = await response.text();
    const content = extractTextFromHTML(html);
    const metadata = extractMetadata(html, url);
    const internalLinks = extractInternalLinks(html, url);
    const chunks = chunkText(content);
    
    return {
      url,
      content,
      metadata,
      internalLinks,
      chunks,
      crawledAt: new Date().toISOString(),
      success: true
    };
  } catch (error) {
    console.error(`Error crawling ${url}:`, error);
    return {
      url,
      content: '',
      metadata: { url, title: '', description: '', keywords: '', headings: [] },
      internalLinks: [],
      chunks: [],
      crawledAt: new Date().toISOString(),
      success: false,
      error: error.message
    };
  }
}

/**
 * Crawl multiple pages with rate limiting
 */
export async function crawlWebsite(startUrl, options = {}) {
  const {
    maxPages = 50,
    delay = 1000,
    maxDepth = 3,
    excludePatterns = ['/api/', '/admin/', '/_next/', '/static/']
  } = options;
  
  const visited = new Set();
  const toVisit = [{ url: startUrl, depth: 0 }];
  const results = [];
  
  while (toVisit.length > 0 && results.length < maxPages) {
    const { url, depth } = toVisit.shift();
    
    if (visited.has(url) || depth > maxDepth) {
      continue;
    }
    
    // Check if URL should be excluded
    if (excludePatterns.some(pattern => url.includes(pattern))) {
      continue;
    }
    
    visited.add(url);
    
    const result = await crawlPage(url);
    results.push(result);
    
    // Add internal links to visit queue if within depth limit
    if (result.success && depth < maxDepth) {
      result.internalLinks.forEach(link => {
        if (!visited.has(link)) {
          toVisit.push({ url: link, depth: depth + 1 });
        }
      });
    }
    
    // Rate limiting
    if (delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  return results;
}
