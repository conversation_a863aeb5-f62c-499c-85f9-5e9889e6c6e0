{"name": "nextjs-temp", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@google/genai": "^0.15.0", "@xenova/transformers": "^2.17.2", "1ms": "^1.0.0", "dotenv": "^16.4.5", "firebase": "^11.0.1", "firebase-admin": "^13.2.0", "framer-motion": "^12.0.11", "jsdom": "^26.1.0", "lucide-react": "^0.454.0", "next": "^15.1.6", "next-firebase-auth-edge": "^1.9.1", "nodemailer": "^6.9.15", "pdfjs-dist": "^5.1.91", "react": "^18", "react-dom": "^18", "react-firebase-hooks": "^5.1.1"}, "devDependencies": {"eslint": "^8", "eslint-config-next": "14.2.15", "postcss": "^8", "tailwindcss": "^3.4.1"}}