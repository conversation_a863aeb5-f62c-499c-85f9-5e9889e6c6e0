/**
 * Simple test to verify API access without authentication
 * Run with: node test-api-access.js
 */

async function testAPIAccess() {
  console.log('🔍 Testing API Access');
  console.log('====================\n');

  const baseUrl = 'http://localhost:3000';
  
  // Test chatbot status endpoint
  try {
    console.log('Testing /api/chatbot/chat (GET)...');
    const response = await fetch(`${baseUrl}/api/chatbot/chat`);
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Chatbot status endpoint accessible');
      console.log('Response:', data);
    } else {
      console.log('❌ Chatbot status endpoint failed');
      console.log('Status:', response.status);
      console.log('Response:', data);
    }
  } catch (error) {
    console.log('❌ Error accessing chatbot status:', error.message);
  }

  console.log('\n' + '-'.repeat(40) + '\n');

  // Test chatbot chat endpoint
  try {
    console.log('Testing /api/chatbot/chat (POST)...');
    const response = await fetch(`${baseUrl}/api/chatbot/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: 'Hello, this is a test message'
      }),
    });
    
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Chatbot chat endpoint accessible');
      console.log('Response success:', data.success);
    } else {
      console.log('❌ Chatbot chat endpoint failed');
      console.log('Status:', response.status);
      console.log('Response:', data);
    }
  } catch (error) {
    console.log('❌ Error accessing chatbot chat:', error.message);
  }

  console.log('\n' + '-'.repeat(40) + '\n');

  // Test crawl status endpoint
  try {
    console.log('Testing /api/chatbot/crawl (GET)...');
    const response = await fetch(`${baseUrl}/api/chatbot/crawl`);
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Crawl status endpoint accessible');
      console.log('Response:', data);
    } else {
      console.log('❌ Crawl status endpoint failed');
      console.log('Status:', response.status);
      console.log('Response:', data);
    }
  } catch (error) {
    console.log('❌ Error accessing crawl status:', error.message);
  }

  console.log('\n🏁 API access test completed!');
}

// Run the test
testAPIAccess().catch(console.error);
