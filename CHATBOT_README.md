# AI Chatbot Implementation

This document describes the AI chatbot implementation for MoneyTales that crawls and indexes website content to answer user questions.

## Overview

The AI chatbot system consists of:
- **Website Crawler**: Automatically crawls and extracts content from website pages
- **Vector Store**: Generates embeddings for content chunks and enables semantic search
- **AI Response Generator**: Uses Google's Gemini AI to generate contextual responses
- **Chat Interface**: User-friendly chat interface with source attribution

## Features

### 🕷️ Website Crawling
- Crawls website pages automatically
- Extracts clean text content from HTML
- Handles internal link discovery
- Configurable crawling depth and rate limiting
- Excludes non-content pages (API routes, static files, etc.)

### 🧠 Intelligent Search
- **Semantic Search**: Uses Xenova TransformerJS for embedding generation
- **Keyword Search**: Traditional keyword matching for exact terms
- **Hybrid Search**: Combines semantic and keyword search for better results
- **Relevance Filtering**: Filters out low-relevance results

### 🤖 AI-Powered Responses
- Uses Google's Gemini AI for natural language responses
- Provides context from relevant website content
- Includes source attribution with clickable links
- Maintains conversation context

### 💬 User Interface
- Clean, modern chat interface
- Real-time typing indicators
- Source expansion with snippets
- Mobile-responsive design
- Error handling and fallbacks

## File Structure

```
├── app/
│   ├── api/chatbot/
│   │   ├── chat/route.js          # Main chat API endpoint
│   │   ├── crawl/route.js         # Website crawling API
│   │   └── status/route.js        # System status API
│   ├── chatbot/
│   │   └── page.js                # Main chatbot page
│   ├── admin/crawl/
│   │   └── page.js                # Admin crawling interface
│   └── components/
│       ├── Chatbot.js             # Main chatbot component
│       └── ChatMessage.js         # Individual message component
├── lib/
│   ├── chatbotService.js          # Core chatbot logic
│   ├── crawler.js                 # Website crawling utilities
│   └── vectorStore.js             # Vector storage and search
└── test-chatbot.js                # Testing script
```

## Setup Instructions

### 1. Environment Variables

Add these to your `.env.local` file:

```bash
# Required for AI responses
GOOGLE_API_KEY=your_gemini_api_key

# Firebase configuration (for data storage)
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
# ... other Firebase config

# Optional: Your website URL for crawling
NEXT_PUBLIC_SITE_URL=https://your-website.com
```

### 2. Firebase Setup

The chatbot uses Firebase Firestore for storing:
- `chatbot_pages`: Page metadata and content
- `chatbot_chunks`: Text chunks with embeddings

Ensure your Firebase project has Firestore enabled.

### 3. Initial Crawling

Before the chatbot can answer questions, you need to crawl and index your website:

1. **Via Admin Interface**: Visit `/admin/crawl` and configure crawling options
2. **Via API**: POST to `/api/chatbot/crawl` with crawl configuration
3. **Via Test Script**: Run `node test-chatbot.js` for automated testing

## Usage

### For Users

1. **Access the Chatbot**: Visit `/chatbot` on your website
2. **Ask Questions**: Type questions about your website, features, or services
3. **View Sources**: Click "Show sources" to see where information came from
4. **Navigate**: Use quick links to access different parts of your site

### For Developers

#### Trigger Website Crawling

```javascript
const response = await fetch('/api/chatbot/crawl', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    startUrl: 'https://your-website.com',
    options: {
      maxPages: 30,
      maxDepth: 2,
      delay: 1000
    }
  })
});
```

#### Send Chat Messages

```javascript
const response = await fetch('/api/chatbot/chat', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    message: 'How do I upload a PDF?'
  })
});
```

#### Check System Status

```javascript
const response = await fetch('/api/chatbot/status');
const status = await response.json();
console.log(`Indexed ${status.stats.totalPages} pages`);
```

## Configuration Options

### Crawling Configuration

```javascript
{
  maxPages: 50,        // Maximum pages to crawl
  delay: 1000,         // Delay between requests (ms)
  maxDepth: 3,         // Maximum crawling depth
  excludePatterns: [   // Patterns to exclude
    '/api/',
    '/admin/',
    '/_next/',
    '/static/'
  ]
}
```

### Search Configuration

- **Similarity Threshold**: 0.3 (filters out low-relevance results)
- **Max Results**: 5 (number of chunks to return)
- **Search Weights**: 70% semantic, 30% keyword matching

## Testing

Run the test script to verify functionality:

```bash
node test-chatbot.js
```

This will:
1. Test basic query processing
2. Crawl sample pages
3. Test queries with indexed content
4. Run multiple topic queries

## Troubleshooting

### Common Issues

1. **No responses from chatbot**
   - Check if website has been crawled (`/api/chatbot/status`)
   - Verify Google API key is configured
   - Check Firebase connection

2. **Poor response quality**
   - Increase crawling depth or page count
   - Check if relevant pages are being indexed
   - Verify content extraction is working

3. **Crawling failures**
   - Check website accessibility
   - Verify URL format
   - Review excluded patterns

### Debug Information

Enable debug logging by checking the browser console and server logs for:
- Crawling progress
- Search results
- AI response generation
- Error messages

## Performance Considerations

- **Crawling**: Rate-limited to prevent server overload
- **Embeddings**: Generated client-side using WebAssembly
- **Search**: Optimized with hybrid semantic/keyword approach
- **Caching**: Firebase provides built-in caching for indexed content

## Future Enhancements

- [ ] Conversation memory and context
- [ ] Multi-language support
- [ ] Advanced analytics and usage tracking
- [ ] Integration with other AI models
- [ ] Automated re-crawling schedules
- [ ] Content freshness detection
