import { NextResponse } from 'next/server';
import {
  authMiddleware,
  redirectToLogin
} from 'next-firebase-auth-edge';

// Allow direct access to these paths without authentication
const PUBLIC_PATHS = [
  '/register',
  '/reset-password',
  '/',
  '/about',
  '/features',
  '/contact',
  '/whatif-ai',
  '/game',
  '/alternate-scenario',
  '/admin/crawl'
];

// Public API routes that don't require authentication
const PUBLIC_API_PATHS = [
  '/api/chatbot/chat',
  '/api/chatbot/crawl',
  '/api/chatbot/status',
  '/api/generate-scenario',
  '/api/generate-image',
  '/api/upload',
  '/api/subscribe',
  '/api/unsubscribe'
];

export async function middleware(request) {
  return authMiddleware(request, {
    loginPath: '/api/login',
    logoutPath: '/api/logout',
    apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
    cookieName: 'AuthToken',
    cookieSignatureKeys: [process.env.COOKIE_SIGNATURE_KEY],
    cookieSerializeOptions: {
      path: '/',
      httpOnly: true,
      secure: process.env.USE_SECURE_COOKIES === "true",
      sameSite: 'lax',
      maxAge: 12 * 60 * 60 * 24 // twelve days
    },
    serviceAccount: {
      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
    },
    enableMultipleCookies: true,
    authorizationHeaderName: 'Authorization',
    handleValidToken: async (_, headers) => {
      // Allow access to public paths
      if (PUBLIC_PATHS.includes(request.nextUrl.pathname)) {
        return NextResponse.next({
          request: {
            headers
          }
        });
      }

      // Allow access to public API routes
      const isPublicAPI = PUBLIC_API_PATHS.some(path =>
        request.nextUrl.pathname.startsWith(path)
      );

      if (isPublicAPI) {
        return NextResponse.next({
          request: {
            headers
          }
        });
      }

      return NextResponse.next({
        request: {
          headers
        }
      });
    },
    handleInvalidToken: async (reason) => {
      // Check if this is a public API route
      if (request.nextUrl.pathname.startsWith("/api")) {
        const isPublicAPI = PUBLIC_API_PATHS.some(path =>
          request.nextUrl.pathname.startsWith(path)
        );

        if (isPublicAPI) {
          // Allow access to public API routes
          return NextResponse.next();
        }

        return new Response(JSON.stringify({message:"Unauthorized"}), {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        });
      }
      console.info('Invalid or missing token: ', reason);
      return redirectToLogin(request, {
        path: '/register',
        publicPaths: PUBLIC_PATHS
      });
    },
    handleError: async (error) => {
      console.error('Auth error:', error);
      return redirectToLogin(request, {
        path: '/register',
        publicPaths: PUBLIC_PATHS
      });
    }
  });
}

export const config = {
  matcher: [
    '/',
    '/((?!_next|api|.*\\.).*)',
    '/api/login',
    '/api/logout',
  ]
};
